# Email Configuration for Password Reset

## Required Environment Variables

Add these environment variables to your `.env.local` file:

```env
# Nodemailer Configuration
NODEMAILER_USER=<EMAIL>
NODEMAILER_APP_PASSWORD=your-app-specific-password
NODEMAILER_FROM_NAME=Your App Name
```

## Gmail Setup Instructions

1. **Enable 2-Factor Authentication** on your Gmail account
2. **Generate App Password**:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate a new app password for "Mail"
   - Use this password as `NODEMAILER_APP_PASSWORD`

3. **Configure Environment Variables**:
   - `NODEMAILER_USER`: Your Gmail address
   - `NODEMAILER_APP_PASSWORD`: The app-specific password (not your regular password)
   - `NODEMAILER_FROM_NAME`: Display name for emails (optional, defaults to "Your App")

## Testing

1. Make sure your database is running and migrated
2. Try the forgot password flow:
   - Go to `/auth/forgot-password`
   - Enter a valid email address
   - Check your email for the reset link
   - Click the link to reset your password

## Rate Limiting

The system includes rate limiting to prevent spam:
- **Forgot Password**: 3 requests per 5 minutes
- **Login Attempts**: 5 requests per minute
- **Email Verification**: 3 requests per 5 minutes

## Email Templates

The system now uses a modular email template system:

### Password Reset Email
- Professional responsive HTML template
- Clear call-to-action button with hover effects
- Security notice with bullet points
- Fallback plain text version
- 1-hour expiration notice
- Branded footer

### Password Reset Confirmation Email
- Sent automatically after successful password reset
- Security tip for unauthorized changes
- Professional styling matching reset email

### Modular Structure
- `lib/email-templates.ts` - All email templates
- `lib/email-service.ts` - Email sending logic
- `lib/email.ts` - Nodemailer transporter
- Easy to add new email types
- Consistent styling across all emails
