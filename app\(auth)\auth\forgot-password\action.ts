"use server";

import { auth } from "@/lib/auth";
import { ActionResult, ForgotPasswordFormData, ForgotPasswordSchema } from "@/lib/schema";
import { APIError } from "better-auth/api";

export async function forgotPassword(
  formData: ForgotPasswordFormData,
): Promise<ActionResult> {
  const parsed = ForgotPasswordSchema.safeParse(formData);

  if (!parsed.success) {
    return {
      success: null,
      error: { reason: parsed.error.flatten().formErrors[0] || "Invalid email" },
    };
  }

  const { email } = parsed.data;

  try {
    // Using auth.api.forgetPassword - Better Auth server-side API
    await auth.api.forgetPassword({
      body: {
        email,
        redirectTo: "/auth/reset-password",
      },
    });

    return {
      success: {
        reason: "Password reset email sent! Check your inbox for further instructions.",
      },
      error: null,
    };
  } catch (error) {
    console.error("Forgot password error:", error);

    if (error instanceof APIError) {
      switch (error.status) {
        case "NOT_FOUND":
          return {
            success: null,
            error: { reason: "No account found with this email address." },
          };
        case "BAD_REQUEST":
          return {
            success: null,
            error: { reason: "Invalid email address." },
          };
        default:
          return {
            success: null,
            error: { reason: error.message || "Failed to send reset email." },
          };
      }
    }

    return {
      success: null,
      error: { reason: "Something went wrong. Please try again." },
    };
  }
}
