"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  ShoppingCart,
  Search,
  Filter,
  Eye,
  Edit,
  DollarSign,
  Package,
  Clock,
  CheckCircle,
  AlertCircle,
  TrendingUp,
} from "lucide-react";

// Mock data - replace with real data from your API
const mockOrders = [
  {
    id: "ORD-001",
    orderNumber: "ORD-2024-001",
    customer: "<PERSON>",
    email: "<EMAIL>",
    amount: 125.50,
    status: "SUBMITTED",
    date: "2024-01-15",
    items: [
      { product: "T-Shirt", type: "Cotton", size: "M", quantity: 2, price: 25.00 },
      { product: "Hoodie", type: "Premium", size: "L", quantity: 1, price: 75.50 },
    ],
  },
  {
    id: "ORD-002",
    orderNumber: "ORD-2024-002",
    customer: "Jane Smith",
    email: "<EMAIL>",
    amount: 89.99,
    status: "CONFIRMED",
    date: "2024-01-15",
    items: [
      { product: "Mug", type: "Ceramic", size: "Standard", quantity: 3, price: 29.99 },
    ],
  },
  {
    id: "ORD-003",
    orderNumber: "ORD-2024-003",
    customer: "Mike Johnson",
    email: "<EMAIL>",
    amount: 156.75,
    status: "PAID",
    date: "2024-01-14",
    items: [
      { product: "T-Shirt", type: "Premium", size: "XL", quantity: 1, price: 35.00 },
      { product: "Hoodie", type: "Premium", size: "L", quantity: 1, price: 75.50 },
      { product: "Cap", type: "Snapback", size: "One Size", quantity: 2, price: 23.125 },
    ],
  },
  {
    id: "ORD-004",
    orderNumber: "ORD-2024-004",
    customer: "Sarah Wilson",
    email: "<EMAIL>",
    amount: 203.25,
    status: "SUBMITTED",
    date: "2024-01-14",
    items: [
      { product: "Hoodie", type: "Premium", size: "M", quantity: 2, price: 75.50 },
      { product: "T-Shirt", type: "Cotton", size: "S", quantity: 2, price: 26.125 },
    ],
  },
  {
    id: "ORD-005",
    orderNumber: "ORD-2024-005",
    customer: "Tom Brown",
    email: "<EMAIL>",
    amount: 78.50,
    status: "DELIVERED",
    date: "2024-01-13",
    items: [
      { product: "T-Shirt", type: "Cotton", size: "L", quantity: 3, price: 26.17 },
    ],
  },
];

const getStatusColor = (status: string) => {
  switch (status) {
    case "SUBMITTED": return "bg-yellow-100 text-yellow-800";
    case "CONFIRMED": return "bg-blue-100 text-blue-800";
    case "PAID": return "bg-green-100 text-green-800";
    case "DELIVERED": return "bg-emerald-100 text-emerald-800";
    case "CANCELLED": return "bg-red-100 text-red-800";
    default: return "bg-gray-100 text-gray-800";
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case "SUBMITTED": return <Clock className="w-3 h-3" />;
    case "CONFIRMED": return <CheckCircle className="w-3 h-3" />;
    case "PAID": return <DollarSign className="w-3 h-3" />;
    case "DELIVERED": return <Package className="w-3 h-3" />;
    default: return <AlertCircle className="w-3 h-3" />;
  }
};

export default function OrdersPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [selectedOrder, setSelectedOrder] = useState<any>(null);

  const filteredOrders = mockOrders.filter((order) => {
    const matchesSearch = 
      order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === "all" || order.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const totalRevenue = mockOrders.reduce((sum, order) => sum + order.amount, 0);
  const pendingOrders = mockOrders.filter(order => order.status === "SUBMITTED").length;
  const completedOrders = mockOrders.filter(order => order.status === "DELIVERED").length;

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Orders Management</h1>
          <p className="text-gray-600">Manage and track all customer orders</p>
        </div>
        <Button>
          <TrendingUp className="w-4 h-4 mr-2" />
          Export Orders
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockOrders.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${totalRevenue.toFixed(2)}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Orders</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pendingOrders}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{completedOrders}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle>Orders</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search orders, customers, or emails..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <Filter className="w-4 h-4 mr-2" />
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="SUBMITTED">Submitted</SelectItem>
                <SelectItem value="CONFIRMED">Confirmed</SelectItem>
                <SelectItem value="PAID">Paid</SelectItem>
                <SelectItem value="DELIVERED">Delivered</SelectItem>
                <SelectItem value="CANCELLED">Cancelled</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Orders Table */}
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Order Number</TableHead>
                <TableHead>Customer</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredOrders.map((order) => (
                <TableRow key={order.id}>
                  <TableCell className="font-medium">{order.orderNumber}</TableCell>
                  <TableCell>
                    <div>
                      <p className="font-medium">{order.customer}</p>
                      <p className="text-sm text-gray-600">{order.email}</p>
                    </div>
                  </TableCell>
                  <TableCell>${order.amount.toFixed(2)}</TableCell>
                  <TableCell>
                    <Badge className={`flex items-center space-x-1 w-fit ${getStatusColor(order.status)}`}>
                      {getStatusIcon(order.status)}
                      <span>{order.status}</span>
                    </Badge>
                  </TableCell>
                  <TableCell>{order.date}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setSelectedOrder(order)}
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl">
                          <DialogHeader>
                            <DialogTitle>Order Details - {order.orderNumber}</DialogTitle>
                          </DialogHeader>
                          {selectedOrder && (
                            <div className="space-y-4">
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <h4 className="font-medium">Customer Information</h4>
                                  <p>{selectedOrder.customer}</p>
                                  <p className="text-sm text-gray-600">{selectedOrder.email}</p>
                                </div>
                                <div>
                                  <h4 className="font-medium">Order Information</h4>
                                  <p>Order: {selectedOrder.orderNumber}</p>
                                  <p>Date: {selectedOrder.date}</p>
                                  <p>Total: ${selectedOrder.amount.toFixed(2)}</p>
                                </div>
                              </div>
                              <div>
                                <h4 className="font-medium mb-2">Order Items</h4>
                                <Table>
                                  <TableHeader>
                                    <TableRow>
                                      <TableHead>Product</TableHead>
                                      <TableHead>Type</TableHead>
                                      <TableHead>Size</TableHead>
                                      <TableHead>Quantity</TableHead>
                                      <TableHead>Price</TableHead>
                                    </TableRow>
                                  </TableHeader>
                                  <TableBody>
                                    {selectedOrder.items.map((item: any, index: number) => (
                                      <TableRow key={index}>
                                        <TableCell>{item.product}</TableCell>
                                        <TableCell>{item.type}</TableCell>
                                        <TableCell>{item.size}</TableCell>
                                        <TableCell>{item.quantity}</TableCell>
                                        <TableCell>${item.price.toFixed(2)}</TableCell>
                                      </TableRow>
                                    ))}
                                  </TableBody>
                                </Table>
                              </div>
                            </div>
                          )}
                        </DialogContent>
                      </Dialog>
                      <Button variant="outline" size="sm">
                        <Edit className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
