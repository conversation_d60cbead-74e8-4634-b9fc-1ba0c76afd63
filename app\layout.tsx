import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";
import DevToolbar from "@/components/dev-tools";
import { Toaster } from "@/components/ui/sonner";

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className="antialiased">
        <DevToolbar />
        {children}
        <Toaster />
      </body>
    </html>
  );
}
