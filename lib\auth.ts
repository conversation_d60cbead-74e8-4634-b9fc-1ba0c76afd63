import { betterAuth } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";
import { prisma } from "@/lib/prisma";
import { nextCookies } from "better-auth/next-js";
import { admin } from "better-auth/plugins/admin";
import { sendEmail } from "@/lib/email";
import { UserRole } from "@/generated/prisma";

export const auth = betterAuth({
  database: prismaAdapter(prisma, {
    provider: "postgresql",
  }),
  socialProviders: {
    google: {
      prompt: "select_account",
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
    },
  },
  emailAndPassword: {
    enabled: true,
    minPasswordLength: 6,
    autoSignIn: false,
    sendResetPassword: async ({ user, url, token }, request) => {
      await sendEmail({
        to: user.email,
        subject: "Reset your password",
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #333; text-align: center;">Reset Your Password</h2>
            <p style="color: #666; font-size: 16px;">Hello ${user.name || 'there'},</p>
            <p style="color: #666; font-size: 16px;">
              We received a request to reset your password. Click the button below to create a new password:
            </p>
            <div style="text-align: center; margin: 30px 0;">
              <a href="${url}"
                 style="background-color: #000; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                Reset Password
              </a>
            </div>
            <p style="color: #666; font-size: 14px;">
              If you didn't request this password reset, you can safely ignore this email. Your password will remain unchanged.
            </p>
            <p style="color: #666; font-size: 14px;">
              This link will expire in 1 hour for security reasons.
            </p>
            <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
            <p style="color: #999; font-size: 12px; text-align: center;">
              If you're having trouble clicking the button, copy and paste this URL into your browser:<br>
              <a href="${url}" style="color: #666; word-break: break-all;">${url}</a>
            </p>
          </div>
        `,
        text: `
          Reset Your Password

          Hello ${user.name || 'there'},

          We received a request to reset your password. Click the link below to create a new password:

          ${url}

          If you didn't request this password reset, you can safely ignore this email. Your password will remain unchanged.

          This link will expire in 1 hour for security reasons.
        `,
      });
    },
    onPasswordReset: async ({ user }, request) => {
      console.log(`Password for user ${user.email} has been reset successfully.`);
    },
  },
  account: {
    accountLinking: {
      enabled: true,
    },
  },
  rateLimit: {
    enabled: true,
    window: 60, // 60 seconds window
    max: 100, // 100 requests per window for general endpoints
    storage: "database", // Store rate limit data in database
    customRules: {
      // Strict rate limiting for forgot password to prevent email spam
      "/request-password-reset": {
        window: 300, // 5 minutes window
        max: 3, // Only 3 forgot password requests per 5 minutes
      },
      // Also limit sign-in attempts
      "/sign-in/email": {
        window: 60, // 1 minute window
        max: 5, // 5 login attempts per minute
      },
      // Limit email verification requests
      "/send-verification-email": {
        window: 300, // 5 minutes window
        max: 3, // Only 3 verification emails per 5 minutes
      },
    },
  },
  user: {
    additionalFields: {
      role: {
        type: ["CUSTOMER", "ADMIN"] as Array<UserRole>,
        input: false,
      },
    },
  },
  advanced: {
    database: {
      generateId: false,
    },
  },
  session: {
    expiresIn: 60 * 60 * 24 * 7, // 7 days
  },
  plugins: [
    nextCookies(),
    admin({
      defaultRole: UserRole.CUSTOMER,
      adminRoles: [UserRole.ADMIN],
    }),
  ],
});
