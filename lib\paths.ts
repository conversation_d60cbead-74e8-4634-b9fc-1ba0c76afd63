// Define public paths that don't require authentication
export function isPublicPath(pathname: string): boolean {
  const publicPaths = [
    "/",
    "/auth/login",
    "/auth/register",
    "/auth/reset-password",
    "/auth/forgot-password",
    "/company",
    "/products",
    "/services",
    "/governance",
    "/careers",
    "/contact",
    "/media",
    "/api/auth"
  ];
  
  return publicPaths.some(path => 
    pathname === path || pathname.startsWith(path + "/")
  );
}
const DEFAULT_LOGIN_REDIRECT = "/dashboard";

export { DEFAULT_LOGIN_REDIRECT };
