"use client";
import { signIn } from "@/lib/auth-client";
import { But<PERSON> } from "@/components/ui/button";
import { useState } from "react";
import { GoogleIcon } from "./ui/icons";

export default function GoogleSignin() {
  const [isPending, setIsPending] = useState(false);

  async function handleClick() {
    setIsPending(true);

    await signIn.social({
      provider: "google",
      callbackURL: "/profile",
      errorCallbackURL: "/auth/login/error",
    });

    setIsPending(false);
  }

  return (
    <Button
      variant="outline"
      className="w-full flex items-center justify-center"
      type="button"
      onClick={handleClick}
      disabled={isPending}
    >
      <GoogleIcon className="mr-2" />
      Google
    </Button>
  );
}
