"use client"

import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { GoogleIcon} from "@/components/ui/icons";
import { signInWithGoogle } from "@/lib/auth-client";
import RegisterForm from "@/components/auth/register-form";
import Logo from "@/components/logo";


export default function RegisterPage() {
  return (
   <div className="flex min-h-screen items-center justify-center bg-neutral-100">
      <div className="flex flex-col items-center w-full max-w-md gap-4">
          
          <div className="flex items-center justify-center">
            <Logo />
           
          </div>
       
          
     
        <Card className="w-full">
          <CardContent className="flex flex-col gap-4 pt-6">
            <RegisterForm />
            <div className="flex items-center my-2">
              <div className="flex-1 h-px bg-muted-foreground/30" />
              <span className="mx-3 text-muted-foreground text-xs font-medium">
                OR
              </span>
              <div className="flex-1 h-px bg-muted-foreground/30" />
            </div>
            <div className="flex flex-row gap-2 w-full">
              <Button
                variant="outline"
                className="w-full flex items-center justify-center"
                type="button"
                onClick={signInWithGoogle}
              >
                <GoogleIcon className="mr-2" />
                Google
              </Button>
            </div>
            <div className="text-center text-sm mt-4">
              Already have an account?{" "}
              <Link
                href="/auth/login"
                className="text-primary underline hover:no-underline font-medium"
              >
                Login
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}