"use server";

import { authClient } from "@/lib/auth-client";
import { ActionResult, ResetPasswordFormData, ResetPasswordSchema } from "@/lib/schema";

export async function resetPassword(
  formData: ResetPasswordFormData & { token: string },
): Promise<ActionResult> {
  const parsed = ResetPasswordSchema.safeParse({
    password: formData.password,
    confirmPassword: formData.confirmPassword,
  });

  if (!parsed.success) {
    const errors = parsed.error.flatten().fieldErrors;
    const firstError = errors.password?.[0] || errors.confirmPassword?.[0] || "Invalid input";
    return {
      success: null,
      error: { reason: firstError },
    };
  }

  const { password } = parsed.data;
  const { token } = formData;

  try {
    // Using authClient.resetPassword - Better Auth client method
    const { error } = await authClient.resetPassword({
      newPassword: password,
      token,
    });

    if (error) {
      return {
        success: null,
        error: { reason: error.message || "Failed to reset password" },
      };
    }

    return {
      success: {
        reason: "Password reset successful! You can now login with your new password.",
      },
      error: null,
    };
  } catch (error) {
    console.error("Reset password error:", error);
    return {
      success: null,
      error: { reason: "Something went wrong. Please try again." },
    };
  }
}
