"use client";

import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Home,
  ShoppingCart,
  BookOpen,
  Briefcase,
  ChevronDown,
  LogOut,
  Settings,
  User,
} from "lucide-react";
import Image from "next/image";
import { motion } from "framer-motion";

import { useEffect, useState } from "react";
import { usePathname, useRouter } from "next/navigation";
import Link from "next/link";
// import { authClient } from "@/lib/auth-client";

export default function Navigation() {
  const pathName = usePathname();
  const router = useRouter();

  const navItems = [
    { href: "/dashboard", icon: Home, label: "Dashboard" },
    { href: "/orders", icon: ShoppingCart, label: "Orders" },
    { href: "/blog", icon: BookOpen, label: "Blog" },
    { href: "/careers", icon: Briefcase, label: "Careers" },
  ];

  // const handleLogout = async () => {
  //   try {
  //     await authClient.signOut();
  //     router.push("/admin/login");
  //     router.refresh();
  //   } catch (error) {
  //     console.error("Logout error:", error);
  //   }
  // };

  return (
    <nav className="bg-black text-white p-4">
      <div className="flex items-center justify-between max-w-7xl mx-auto">
        <div className="flex items-center gap-8">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-teal-500 rounded-full flex items-center justify-center">
              <Image src="/logo.png" alt="Logo" width={41} height={41} />
            </div>
            <span className="font-semibold">FT Connect</span>
          </div>
        </div>

        <div className="flex items-center gap-6">
          {navItems.map(({ href, icon: Icon, label }) => {
            const isActive = pathName === href;
            return (
              <Link href={href} key={href} style={{ textDecoration: "none" }}>
                <motion.div
                  animate={{
                    scale: isActive ? 1.05 : 1,
                    backgroundColor: isActive ? "#1f2937" : "rgba(0,0,0,0)",
                  }}
                  transition={{ type: "spring", stiffness: 200, damping: 18 }}
                  style={{ borderRadius: 100 }}
                  className={
                    isActive
                      ? "backdrop-blur-[3.9px] shadow-[0_0_22px_0_#F2F2F280_inset,0_0_0_1px_#999_inset,-2px_-2px_1px_-2px_#B3B3B3_inset,2px_2px_1px_-2px_#B3B3B3_inset,3px_3px_0.5px_-3.5px_#FFFFFF80_inset]"
                      : ""
                  }
                >
                  <Button
                    variant="ghost"
                    size="sm"
                    className={`text-white rounded-full flex items-center ${
                      isActive ? "bg-transparent text-white" : ""
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    {isActive && (
                      <motion.span
                        initial={{ x: 12, opacity: 0 }}
                        animate={{ x: 0, opacity: 1 }}
                        exit={{ x: 12, opacity: 0 }}
                        transition={{
                          type: "tween",
                          duration: 0.18,
                          ease: "easeOut",
                        }}
                        className="ml-2"
                      >
                        {label}
                      </motion.span>
                    )}
                  </Button>
                </motion.div>
              </Link>
            );
          })}
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="flex items-center gap-2 text-white hover:bg-transparent"
            >
              <Avatar className="w-8 h-8">
                <AvatarFallback>{"AD"}</AvatarFallback>
              </Avatar>
              <div className="text-left">
                <div className="text-sm font-medium">{"Admin"}</div>
                <div className="text-xs text-gray-300">{"Administrator"}</div>
              </div>
              <ChevronDown className="w-4 h-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="bg-black text-white">
            <DropdownMenuItem asChild>
              <Link href="/profile" className="flex items-center w-full">
                <User className="w-4 h-4 mr-2" />
                Profile
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href="/settings" className="flex items-center w-full">
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem>
              <LogOut className="w-4 h-4 mr-2" />
              Sign out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </nav>
  );
}
